#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频验证工具 - 检查视频文件的音频信息
"""

import os
import subprocess
from moviepy.editor import VideoFileClip

def check_with_moviepy(video_path):
    """使用MoviePy检查音频"""
    try:
        video = VideoFileClip(video_path)
        if video.audio:
            print(f"✅ MoviePy检测: {video_path}")
            print(f"   - 音频时长: {video.audio.duration:.2f}秒")
            print(f"   - 视频时长: {video.duration:.2f}秒")
            
            # 尝试获取音频采样率
            try:
                fps = video.audio.fps
                print(f"   - 音频采样率: {fps} Hz")
            except:
                print("   - 无法获取音频采样率")
                
        else:
            print(f"❌ MoviePy检测: {video_path} 无音频")
        
        has_audio = video.audio is not None
        video.close()
        return has_audio
    except Exception as e:
        print(f"❌ MoviePy检测失败: {e}")
        return False

def check_with_ffprobe(video_path):
    """使用ffprobe检查音频（如果可用）"""
    try:
        cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)
            
            audio_streams = [s for s in data['streams'] if s['codec_type'] == 'audio']
            video_streams = [s for s in data['streams'] if s['codec_type'] == 'video']
            
            print(f"✅ FFprobe检测: {video_path}")
            print(f"   - 视频流: {len(video_streams)} 个")
            print(f"   - 音频流: {len(audio_streams)} 个")
            
            for i, stream in enumerate(audio_streams):
                codec = stream.get('codec_name', '未知')
                duration = stream.get('duration', '未知')
                sample_rate = stream.get('sample_rate', '未知')
                print(f"   - 音频流 {i+1}: {codec}, 时长: {duration}s, 采样率: {sample_rate}Hz")
            
            return len(audio_streams) > 0
        else:
            print(f"❌ FFprobe检测失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("⚠️ FFprobe未安装，跳过检测")
        return None
    except Exception as e:
        print(f"❌ FFprobe检测异常: {e}")
        return False

def get_file_info(video_path):
    """获取文件基本信息"""
    try:
        size = os.path.getsize(video_path)
        print(f"📁 文件信息: {video_path}")
        print(f"   - 文件大小: {size:,} bytes ({size/1024/1024:.2f} MB)")
        return True
    except Exception as e:
        print(f"❌ 获取文件信息失败: {e}")
        return False

def main():
    """主函数"""
    print("视频音频验证工具")
    print("=" * 60)
    
    # 查找所有MP4文件
    mp4_files = [f for f in os.listdir('.') if f.endswith('.mp4')]
    
    if not mp4_files:
        print("未找到MP4文件")
        return
    
    print(f"找到 {len(mp4_files)} 个MP4文件\n")
    
    for video_file in mp4_files:
        print("-" * 60)
        
        # 基本文件信息
        get_file_info(video_file)
        print()
        
        # MoviePy检测
        moviepy_result = check_with_moviepy(video_file)
        print()
        
        # FFprobe检测
        ffprobe_result = check_with_ffprobe(video_file)
        print()
        
        # 总结
        if moviepy_result:
            print(f"🎵 {video_file} 包含音频")
        else:
            print(f"🔇 {video_file} 不包含音频")
        
        print()
    
    print("=" * 60)
    print("验证完成！")
    print("\n如果视频包含音频但播放时听不到声音，可能的原因：")
    print("1. 播放器不支持该音频编码")
    print("2. 系统音量设置问题")
    print("3. 音频驱动问题")
    print("4. 视频播放器设置问题")
    print("\n建议：")
    print("- 尝试使用VLC播放器")
    print("- 检查系统音量设置")
    print("- 尝试其他视频播放器")

if __name__ == '__main__':
    main()
