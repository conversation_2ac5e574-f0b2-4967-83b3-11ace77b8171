#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能的脚本
"""

import subprocess
import os
import time

def run_test(name, cmd):
    """运行测试"""
    print(f"\n{'='*60}")
    print(f"测试: {name}")
    print(f"{'='*60}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 60)
    
    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        end_time = time.time()
        
        print(f"执行时间: {end_time - start_time:.2f}秒")
        
        if result.returncode == 0:
            print("✅ 测试成功")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print("❌ 测试失败")
            if result.stderr:
                print("错误:")
                print(result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def main():
    """主测试函数"""
    print("iPhone风格回忆幻灯片生成器 - 新功能测试")
    
    tests = [
        {
            'name': '1. 基本功能测试 - 带标题页和音乐',
            'cmd': [
                'python', 'memory_slideshow.py',
                '--input', './images',
                '--output', 'test1_basic.mp4',
                '--music', '月亮照山川.mp3',
                '--title', '基本功能测试',
                '--slide-duration', '2.0'
            ]
        },
        {
            'name': '2. 完整图片显示测试 - 不裁剪',
            'cmd': [
                'python', 'memory_slideshow.py',
                '--input', './images',
                '--output', 'test2_contain.mp4',
                '--title', '完整图片显示',
                '--slide-duration', '2.5',
                '--transition-duration', '0.8'
            ]
        },
        {
            'name': '3. 无标题页测试',
            'cmd': [
                'python', 'memory_slideshow.py',
                '--input', './images',
                '--output', 'test3_no_title.mp4',
                '--music', '月亮照山川.mp3',
                '--no-title-page',
                '--slide-duration', '2.0'
            ]
        },
        {
            'name': '4. 无日期显示测试',
            'cmd': [
                'python', 'memory_slideshow.py',
                '--input', './images',
                '--output', 'test4_no_date.mp4',
                '--title', '无日期显示',
                '--no-date',
                '--slide-duration', '1.5'
            ]
        },
        {
            'name': '5. 自定义分辨率测试',
            'cmd': [
                'python', 'memory_slideshow.py',
                '--input', './images',
                '--output', 'test5_720p.mp4',
                '--title', '720P测试',
                '--resolution', '1280x720',
                '--slide-duration', '2.0'
            ]
        },
        {
            'name': '6. 快节奏测试 - 短时长多转场',
            'cmd': [
                'python', 'memory_slideshow.py',
                '--input', './images',
                '--output', 'test6_fast.mp4',
                '--music', '月亮照山川.mp3',
                '--title', '快节奏测试',
                '--slide-duration', '1.0',
                '--transition-duration', '0.5'
            ]
        }
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test in tests:
        success = run_test(test['name'], test['cmd'])
        if success:
            success_count += 1
        
        # 检查输出文件是否存在
        output_file = None
        for i, arg in enumerate(test['cmd']):
            if arg == '--output' and i + 1 < len(test['cmd']):
                output_file = test['cmd'][i + 1]
                break
        
        if output_file and os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"输出文件: {output_file} ({file_size:,} bytes)")
        else:
            print(f"⚠️ 输出文件未找到: {output_file}")
    
    print(f"\n{'='*60}")
    print(f"测试总结: {success_count}/{total_tests} 个测试通过")
    print(f"{'='*60}")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 个测试失败")
    
    # 显示生成的文件
    print("\n生成的测试文件:")
    for file in os.listdir('.'):
        if file.startswith('test') and file.endswith('.mp4'):
            size = os.path.getsize(file)
            print(f"  {file} - {size:,} bytes")

if __name__ == '__main__':
    main()
