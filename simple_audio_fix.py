#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单音频修复 - 创建一个确保有声音的视频
"""

from moviepy.editor import *
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def create_audio_test_video():
    """创建一个简单的测试视频，确保音频可以播放"""
    print("创建音频测试视频...")
    
    # 创建一个简单的测试图片
    img = Image.new('RGB', (1920, 1080), (50, 50, 100))
    draw = ImageDraw.Draw(img)
    
    # 添加文字
    try:
        font = ImageFont.truetype("arial.ttf", 72)
    except:
        font = ImageFont.load_default()
    
    text = "音频测试"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (1920 - text_width) // 2
    y = (1080 - text_height) // 2
    
    draw.text((x, y), text, font=font, fill='white')
    
    # 转换为numpy数组
    img_array = np.array(img)
    
    # 创建10秒的视频剪辑
    video_clip = ImageClip(img_array).set_duration(10.0)
    
    try:
        # 加载音频文件
        print("加载音频文件...")
        audio_clip = AudioFileClip("月亮照山川.mp3")
        
        # 裁剪音频到10秒
        audio_clip = audio_clip.subclip(0, 10.0)
        print(f"音频时长: {audio_clip.duration}秒")
        
        # 合并视频和音频
        final_clip = video_clip.set_audio(audio_clip)
        
        # 尝试不同的导出方式
        export_configs = [
            {
                'name': 'MP3音频',
                'file': 'audio_test_mp3.mp4',
                'params': {
                    'fps': 30,
                    'codec': 'libx264',
                    'audio_codec': 'mp3',
                    'audio_bitrate': '192k'
                }
            },
            {
                'name': '无压缩音频',
                'file': 'audio_test_pcm.mp4',
                'params': {
                    'fps': 30,
                    'codec': 'libx264',
                    'audio_codec': 'pcm_s16le'
                }
            },
            {
                'name': '默认设置',
                'file': 'audio_test_default.mp4',
                'params': {
                    'fps': 30,
                    'codec': 'libx264'
                }
            }
        ]
        
        for config in export_configs:
            try:
                print(f"\n导出 {config['name']}...")
                final_clip.write_videofile(
                    config['file'],
                    verbose=False,
                    logger=None,
                    **config['params']
                )
                print(f"✅ {config['name']} 导出成功: {config['file']}")
            except Exception as e:
                print(f"❌ {config['name']} 导出失败: {e}")
        
        # 清理资源
        audio_clip.close()
        video_clip.close()
        final_clip.close()
        
        print("\n音频测试视频创建完成！")
        print("请尝试播放以下文件:")
        for config in export_configs:
            print(f"  - {config['file']}")
        
    except Exception as e:
        print(f"❌ 音频测试失败: {e}")
        import traceback
        traceback.print_exc()

def create_no_compression_video():
    """创建无压缩的高质量音频视频"""
    print("\n创建无压缩音频视频...")
    
    try:
        # 直接使用原始音频，不做任何处理
        audio_clip = AudioFileClip("月亮照山川.mp3").subclip(0, 5.0)
        
        # 创建简单视频
        img = Image.new('RGB', (1920, 1080), (100, 0, 0))
        video_clip = ImageClip(np.array(img)).set_duration(5.0)
        
        # 合并
        final_clip = video_clip.set_audio(audio_clip)
        
        # 使用最兼容的设置导出
        final_clip.write_videofile(
            'high_quality_audio.mp4',
            fps=30,
            codec='libx264',
            audio_codec='aac',
            audio_bitrate='320k',  # 高比特率
            bitrate='8000k',       # 高视频比特率
            verbose=False
        )
        
        print("✅ 高质量音频视频创建完成: high_quality_audio.mp4")
        
        # 清理
        audio_clip.close()
        video_clip.close()
        final_clip.close()
        
    except Exception as e:
        print(f"❌ 高质量视频创建失败: {e}")

def main():
    """主函数"""
    print("音频修复工具")
    print("=" * 50)
    
    # 检查音频文件是否存在
    import os
    if not os.path.exists("月亮照山川.mp3"):
        print("❌ 找不到音频文件: 月亮照山川.mp3")
        return
    
    # 创建测试视频
    create_audio_test_video()
    
    # 创建高质量视频
    create_no_compression_video()
    
    print("\n" + "=" * 50)
    print("修复完成！")
    print("\n如果这些测试视频仍然没有声音，问题可能是:")
    print("1. 系统音频驱动问题")
    print("2. 播放器设置问题") 
    print("3. 音频设备问题")
    print("\n建议:")
    print("- 尝试在不同的设备上播放")
    print("- 使用VLC媒体播放器")
    print("- 检查Windows音频设置")

if __name__ == '__main__':
    main()
