# iPhone风格回忆幻灯片生成器

一个精简但功能完整的Python工具，用于创建类似iPhone相册回忆的幻灯片视频。

## 🎬 功能特色

- **15种PPT风格转场效果** - 每张图片使用不同转场，避免单调
- **智能日期显示** - 自动提取EXIF拍摄日期，无日期时显示当前日期
- **背景音乐融合** - 自动调整音乐长度，专业淡入淡出效果
- **智能图片处理** - 自动调整尺寸，保持比例，居中裁剪
- **简单易用** - 一行命令即可生成专业视频

## 🚀 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv photo2video_env

# 激活虚拟环境 (Windows)
photo2video_env\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source photo2video_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 基本使用
```bash
# 最简单的使用
python memory_slideshow.py --input ./images --output my_memory.mp4

# 添加背景音乐
python memory_slideshow.py --input ./images --output my_memory.mp4 --music "月亮照山川.mp3"

# 使用批处理脚本 (Windows)
create_memory.bat ./images my_memory.mp4 "月亮照山川.mp3"
```

### 3. 查看示例
```bash
# 运行示例演示
python examples.py

# 使用预设配置
python config_example.py high_quality ./images 4k_video.mp4 music.mp3
```

## 📁 项目文件

- `memory_slideshow.py` - 主程序
- `requirements.txt` - 依赖列表
- `使用说明.md` - 详细使用说明
- `examples.py` - 使用示例
- `config_example.py` - 配置示例
- `create_memory.bat` - Windows批处理脚本

## ✅ 测试结果

已成功测试并生成了 `test_memory.mp4`，包含：
- 9张JPG图片 + 1个MP4视频
- 背景音乐：月亮照山川.mp3
- 多种转场效果
- 日期文字叠加

## 🎯 转场效果

包含15种不同的转场效果：
- fade, push_left, push_right, push_up, push_down
- wipe_left, wipe_right, zoom_in, zoom_out
- slide_left, slide_right, rotate_in
- blinds_h, blinds_v, checkerboard

## 📱 支持格式

**图片**: JPG, PNG, BMP, TIFF
**视频**: MP4, MOV, AVI, MKV
**音频**: MP3, WAV, AAC, M4A

## 🛠️ 技术特点

- **瑞士军刀式设计** - 精简但功能完整
- **无过度开发** - 专注核心功能
- **易于扩展** - 模块化设计
- **跨平台支持** - Windows/Linux/Mac