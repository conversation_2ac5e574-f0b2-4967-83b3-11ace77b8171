# iPhone风格回忆幻灯片生成器 - 动态视频和音频混合

## 🎯 最新功能

### ✅ 动态视频支持
- **视频保持动态播放** - 不再只提取第一帧
- **智能时长调整** - 自动调整视频长度匹配幻灯片时长
- **统一分辨率处理** - 所有视频调整为目标分辨率
- **降级处理机制** - 如果视频处理失败，自动降级为静态图片

### ✅ 音频混合功能
- **背景音乐 + 视频音频** - 智能混合多个音频源
- **音量平衡** - 视频音频降低音量(30%)，避免与背景音乐冲突
- **时间同步** - 视频音频在正确的时间点播放
- **兼容性处理** - 支持有声视频和无声视频

## 🔧 技术实现

### 视频处理流程
```
扫描媒体文件 → 预处理视频(调整尺寸/时长) → 添加日期文字 → 创建动态剪辑
```

### 音频混合流程
```
背景音乐处理 → 收集视频音频 → 音量调整 → 音频合成 → 最终混合
```

## 📊 处理示例

### 成功处理
```
预处理媒体文件到统一分辨率...
预处理 1/10: video.mp4 ✅
预处理 2/10: image.jpg ✅
...
发现 2 个视频音频，进行混合...
音频混合成功
```

### 降级处理
```
预处理 1/10: problematic_video.mp4
视频处理失败，转为静态图片: [错误信息]
降级处理成功 ✅
```

## 🎬 功能特性

### 1. 视频动态播放
- **保持原始动态效果** - 视频在幻灯片中正常播放
- **时长智能调整**:
  - 视频太长 → 裁剪到幻灯片时长
  - 视频太短 → 循环播放填满时长
- **分辨率统一** - 所有视频调整为1920x1080

### 2. 音频智能混合
- **多音频源支持**:
  - 背景音乐 (主音频，音量70%)
  - 视频原声 (辅助音频，音量30%)
- **时间同步** - 视频音频在对应时间点播放
- **降级兼容** - 如果混合失败，保留背景音乐

### 3. 文字叠加增强
- **图片文字叠加** - 直接在图片上添加日期
- **视频文字叠加** - 使用透明图层叠加到视频上
- **统一样式** - 所有媒体使用相同的文字样式

## 🛠️ 使用方式

### 基本使用
```bash
python memory_slideshow.py \
  --input ./mixed_media \
  --output dynamic_slideshow.mp4 \
  --music "background.mp3" \
  --title "动态回忆"
```

### 批处理脚本
```bash
create_memory.bat ./mixed_media "动态回忆.mp4" "background.mp3" "我的动态回忆"
```

## 📁 支持的媒体类型

### 图片文件
- JPG, PNG, BMP, TIFF
- 处理为静态幻灯片
- 添加日期文字叠加

### 视频文件
- MP4, MOV, AVI, MKV
- **优先**: 保持动态播放 + 音频
- **降级**: 提取首帧为静态图片

### 音频文件
- MP3, WAV, AAC, M4A
- 背景音乐 + 视频原声混合

## 🎯 处理逻辑

### 视频处理优先级
1. **尝试动态处理** - 调整尺寸、时长、添加文字
2. **降级为静态** - 如果动态处理失败，提取首帧
3. **跳过文件** - 如果完全无法处理

### 音频混合策略
1. **仅背景音乐** - 如果没有视频音频
2. **仅视频音频** - 如果没有背景音乐
3. **智能混合** - 背景音乐 + 视频音频(降低音量)

## 📈 性能优化

### 内存管理
- **流式处理** - 逐个处理媒体文件
- **及时释放** - 处理完成后立即释放资源
- **错误恢复** - 单个文件失败不影响整体

### 兼容性处理
- **PIL版本兼容** - 自动适配新旧版本
- **MoviePy容错** - 视频处理失败时的降级机制
- **音频格式兼容** - 支持多种音频编码

## 🎨 输出效果

### 视频规格
- **分辨率**: 1920x1080
- **帧率**: 30fps
- **编码**: H.264 + MP3音频
- **动态内容**: 视频保持原始动态效果

### 音频规格
- **背景音乐**: MP3 192kbps, 音量70%
- **视频音频**: 原始格式, 音量30%
- **混合效果**: 专业级音频合成

## 🚨 注意事项

### 1. 视频兼容性
- 某些视频格式可能无法动态处理
- 程序会自动降级为静态图片
- 不影响整体生成效果

### 2. 音频同步
- 视频音频在对应时间点播放
- 背景音乐贯穿整个视频
- 音量平衡避免冲突

### 3. 性能考虑
- 视频文件较大时处理时间较长
- 建议使用高质量但不过大的视频
- 混合媒体比纯图片处理稍慢

## ✅ 成功标志

当看到以下输出时，说明动态视频功能正常：
```
预处理媒体文件到统一分辨率...
预处理 X/Y: video.mp4 ✅
发现 N 个视频音频，进行混合...
音频混合成功
✅ 成功创建回忆视频
```

现在的幻灯片生成器支持真正的动态视频播放和智能音频混合，提供更丰富的视觉和听觉体验！🎬🎵
