# iPhone风格回忆幻灯片生成器

一个精简但功能完整的Python工具，用于创建类似iPhone相册回忆的幻灯片视频。

## 功能特色

- 🎬 **20种PPT风格转场效果** - 每张图片使用不同转场，包括分割、盒子、圆形等高级效果
- 📅 **智能日期显示** - 自动提取EXIF拍摄日期，优雅显示在图片下方
- 🎵 **背景音乐融合** - 自动调整音乐长度，专业淡入淡出效果，确保音频正常播放
- 🖼️ **完整图片显示** - 显示图片全部内容，不裁剪，保持原始比例
- 📖 **标题页展示** - 首页显示大字体主题和日期范围，专业开场
- ⚡ **简单易用** - 一行命令即可生成专业视频

## 安装依赖

```bash
# 激活虚拟环境
photo2video_env\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

## 基本使用

### 最简单的使用方式
```bash
python memory_slideshow.py --input ./images --output my_memory.mp4
```

### 添加背景音乐和主题
```bash
python memory_slideshow.py --input ./images --output my_memory.mp4 --music "月亮照山川.mp3" --title "我的美好回忆"
```

### 自定义参数
```bash
python memory_slideshow.py \
  --input ./images \
  --output custom_memory.mp4 \
  --music background.mp3 \
  --title "自定义主题" \
  --slide-duration 3.0 \
  --transition-duration 1.0 \
  --resolution 1920x1080
```

### 无标题页和日期
```bash
python memory_slideshow.py --input ./images --output simple.mp4 --no-title-page --no-date
```

## 命令行参数

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--input` | `-i` | 必需 | 输入图片目录 |
| `--output` | `-o` | `memory_slideshow.mp4` | 输出视频文件名 |
| `--music` | `-m` | 无 | 背景音乐文件 |
| `--slide-duration` | | `3.0` | 每张图片显示时长(秒) |
| `--transition-duration` | | `1.0` | 转场效果时长(秒) |
| `--no-date` | | `False` | 不显示日期文字 |
| `--resolution` | | `1920x1080` | 输出分辨率 |

## 支持的文件格式

### 图片格式
- JPG/JPEG
- PNG  
- BMP
- TIFF

### 视频格式
- MP4
- MOV
- AVI
- MKV

### 音频格式
- MP3
- WAV
- AAC
- M4A

## 转场效果列表

程序包含15种不同的转场效果，会随机分配给每张图片：

1. **fade** - 淡入淡出
2. **push_left** - 从左推入
3. **push_right** - 从右推入  
4. **push_up** - 从上推入
5. **push_down** - 从下推入
6. **wipe_left** - 从左擦除
7. **wipe_right** - 从右擦除
8. **zoom_in** - 缩放进入
9. **zoom_out** - 缩放退出
10. **slide_left** - 向左滑动
11. **slide_right** - 向右滑动
12. **rotate_in** - 旋转进入
13. **blinds_h** - 水平百叶窗
14. **blinds_v** - 垂直百叶窗
15. **checkerboard** - 棋盘格

## 使用示例

### 示例1：创建家庭回忆视频
```bash
python memory_slideshow.py \
  --input "D:/Photos/Family2024" \
  --output "家庭回忆2024.mp4" \
  --music "温馨背景音乐.mp3" \
  --slide-duration 3.5
```

### 示例2：创建旅行回忆视频
```bash
python memory_slideshow.py \
  --input "./旅行照片" \
  --output "旅行回忆.mp4" \
  --music "轻快音乐.mp3" \
  --transition-duration 0.8 \
  --resolution 1280x720
```

### 示例3：快速预览（无音乐）
```bash
python memory_slideshow.py \
  --input ./images \
  --output preview.mp4 \
  --slide-duration 2.0 \
  --transition-duration 0.5
```

## 注意事项

1. **图片质量**: 建议使用高质量图片，程序会自动调整尺寸
2. **音乐长度**: 程序会自动调整音乐长度匹配视频时长
3. **处理时间**: 转场效果较多，处理时间可能较长，请耐心等待
4. **内存使用**: 大量高分辨率图片可能占用较多内存
5. **日期显示**: 优先显示EXIF拍摄日期，无日期时显示当前日期

## 输出质量

- **分辨率**: 支持自定义，默认1920x1080
- **帧率**: 30fps
- **编码**: H.264视频编码，AAC音频编码
- **质量**: 高质量输出，适合分享和保存

## 故障排除

### 常见问题

1. **缺少依赖库**
   ```bash
   pip install pillow moviepy exifread numpy
   ```

2. **字体问题**
   - Windows: 自动使用微软雅黑
   - 其他系统: 使用默认字体

3. **音频问题**
   - 确保音频文件格式正确
   - 检查文件路径是否正确

4. **内存不足**
   - 减少图片数量
   - 降低输出分辨率
   - 缩短单张图片时长

这个工具设计为"瑞士军刀"式的精简实现，专注于核心功能，易于使用和扩展。
