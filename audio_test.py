#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频测试脚本 - 验证音频是否正确嵌入到视频中
"""

from moviepy.editor import *
import numpy as np
from PIL import Image

def create_simple_test():
    """创建简单的音频测试视频"""
    print("创建简单音频测试...")
    
    # 创建一个简单的红色图片
    img = Image.new('RGB', (1920, 1080), (255, 0, 0))
    img_array = np.array(img)
    
    # 创建5秒的视频剪辑
    video_clip = ImageClip(img_array).set_duration(5.0)
    
    # 加载音频
    try:
        audio_clip = AudioFileClip("月亮照山川.mp3").subclip(0, 5.0)
        print(f"音频加载成功，时长: {audio_clip.duration}秒")
        
        # 合并视频和音频
        final_clip = video_clip.set_audio(audio_clip)
        
        # 输出测试
        print("导出测试视频...")
        final_clip.write_videofile(
            "simple_audio_test.mp4",
            fps=30,
            codec='libx264',
            audio_codec='aac',
            verbose=False
        )
        
        print("✅ 简单音频测试完成: simple_audio_test.mp4")
        
        # 清理资源
        audio_clip.close()
        video_clip.close()
        final_clip.close()
        
    except Exception as e:
        print(f"❌ 音频测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_audio_formats():
    """测试不同的音频格式和编码"""
    print("\n测试不同音频编码...")
    
    # 创建测试图片
    img = Image.new('RGB', (1920, 1080), (0, 255, 0))
    img_array = np.array(img)
    video_clip = ImageClip(img_array).set_duration(3.0)
    
    try:
        audio_clip = AudioFileClip("月亮照山川.mp3").subclip(0, 3.0)
        final_clip = video_clip.set_audio(audio_clip)
        
        # 测试不同的编码参数
        test_configs = [
            {
                'name': 'AAC编码',
                'file': 'test_aac.mp4',
                'params': {
                    'fps': 30,
                    'codec': 'libx264',
                    'audio_codec': 'aac',
                    'audio_bitrate': '128k'
                }
            },
            {
                'name': 'MP3编码',
                'file': 'test_mp3.mp4',
                'params': {
                    'fps': 30,
                    'codec': 'libx264',
                    'audio_codec': 'mp3',
                    'audio_bitrate': '192k'
                }
            },
            {
                'name': '默认编码',
                'file': 'test_default.mp4',
                'params': {
                    'fps': 30,
                    'codec': 'libx264'
                }
            }
        ]
        
        for config in test_configs:
            try:
                print(f"测试 {config['name']}...")
                final_clip.write_videofile(
                    config['file'],
                    verbose=False,
                    **config['params']
                )
                print(f"✅ {config['name']} 成功: {config['file']}")
            except Exception as e:
                print(f"❌ {config['name']} 失败: {e}")
        
        # 清理资源
        audio_clip.close()
        video_clip.close()
        final_clip.close()
        
    except Exception as e:
        print(f"❌ 音频格式测试失败: {e}")

def check_video_audio(video_path):
    """检查视频文件是否包含音频"""
    try:
        video = VideoFileClip(video_path)
        if video.audio:
            print(f"✅ {video_path} 包含音频，时长: {video.audio.duration:.2f}秒")
        else:
            print(f"❌ {video_path} 不包含音频")
        video.close()
    except Exception as e:
        print(f"❌ 检查 {video_path} 失败: {e}")

def main():
    """主函数"""
    print("音频测试工具")
    print("=" * 50)
    
    # 检查现有视频文件的音频
    print("检查现有视频文件...")
    import os
    for file in os.listdir('.'):
        if file.endswith('.mp4'):
            check_video_audio(file)
    
    print("\n" + "=" * 50)
    
    # 创建简单测试
    create_simple_test()
    
    # 测试不同编码
    test_audio_formats()
    
    print("\n" + "=" * 50)
    print("测试完成！请检查生成的测试文件是否有声音。")

if __name__ == '__main__':
    main()
