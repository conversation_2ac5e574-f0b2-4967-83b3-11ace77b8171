#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone风格回忆幻灯片生成器
精简但功能完整的实现
"""

import os
import sys
import random
import argparse
from datetime import datetime
from pathlib import Path
import json

try:
    from PIL import Image, ImageDraw, ImageFont, ImageFilter
    import numpy as np
    from moviepy.editor import *
    import exifread
except ImportError as e:
    print(f"缺少依赖库: {e}")
    print("请运行: pip install pillow moviepy exifread numpy")
    sys.exit(1)


class MemorySlideshowGenerator:
    """iPhone风格回忆幻灯片生成器"""
    
    def __init__(self, config=None):
        self.config = config or self.default_config()
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        self.supported_video_formats = {'.mp4', '.mov', '.avi', '.mkv'}
        self.supported_audio_formats = {'.mp3', '.wav', '.aac', '.m4a'}
        
        # 转场效果列表
        self.transitions = [
            'fade', 'push_left', 'push_right', 'push_up', 'push_down',
            'wipe_left', 'wipe_right', 'zoom_in', 'zoom_out', 'slide_left',
            'slide_right', 'rotate_in', 'blinds_h', 'blinds_v', 'checkerboard'
        ]
    
    def default_config(self):
        """默认配置"""
        return {
            'output': {
                'resolution': (1920, 1080),
                'fps': 30,
                'quality': 'high'
            },
            'slideshow': {
                'slide_duration': 3.0,
                'transition_duration': 1.0
            },
            'date_text': {
                'show_date': True,
                'font_size': 36,
                'color': 'white',
                'position': 'bottom_right',
                'margin': (50, 50),
                'shadow': True
            },
            'music': {
                'fade_in': 2.0,
                'fade_out': 3.0,
                'volume': 0.7
            }
        }
    
    def extract_date_from_image(self, image_path):
        """从图片EXIF提取拍摄日期"""
        try:
            with open(image_path, 'rb') as f:
                tags = exifread.process_file(f, stop_tag='EXIF DateTimeOriginal')
                if 'EXIF DateTimeOriginal' in tags:
                    date_str = str(tags['EXIF DateTimeOriginal'])
                    return datetime.strptime(date_str, '%Y:%m:%d %H:%M:%S')
        except:
            pass
        return datetime.now()
    
    def format_date(self, date_obj):
        """格式化日期显示"""
        return date_obj.strftime('%Y年%m月%d日')
    
    def add_date_text(self, image, date_text):
        """在图片上添加日期文字"""
        img = image.copy()
        draw = ImageDraw.Draw(img)
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("msyh.ttc", self.config['date_text']['font_size'])
        except:
            try:
                font = ImageFont.truetype("arial.ttf", self.config['date_text']['font_size'])
            except:
                font = ImageFont.load_default()
        
        # 计算文字位置
        bbox = draw.textbbox((0, 0), date_text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        margin_x, margin_y = self.config['date_text']['margin']
        x = img.width - text_width - margin_x
        y = img.height - text_height - margin_y
        
        # 添加阴影
        if self.config['date_text']['shadow']:
            draw.text((x+2, y+2), date_text, font=font, fill='black')
        
        # 添加文字
        draw.text((x, y), date_text, font=font, fill=self.config['date_text']['color'])
        
        return img
    
    def resize_image(self, image, target_size):
        """调整图片尺寸，保持比例"""
        img_ratio = image.width / image.height
        target_ratio = target_size[0] / target_size[1]
        
        if img_ratio > target_ratio:
            # 图片更宽，以高度为准
            new_height = target_size[1]
            new_width = int(new_height * img_ratio)
        else:
            # 图片更高，以宽度为准
            new_width = target_size[0]
            new_height = int(new_width / img_ratio)
        
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 居中裁剪
        left = (new_width - target_size[0]) // 2
        top = (new_height - target_size[1]) // 2
        right = left + target_size[0]
        bottom = top + target_size[1]
        
        return resized.crop((left, top, right, bottom))
    
    def create_transition_clip(self, img1, img2, transition_type, duration):
        """创建转场效果"""
        fps = self.config['output']['fps']
        
        if transition_type == 'fade':
            return self.fade_transition(img1, img2, duration, fps)
        elif transition_type.startswith('push_'):
            direction = transition_type.split('_')[1]
            return self.push_transition(img1, img2, direction, duration, fps)
        elif transition_type.startswith('wipe_'):
            direction = transition_type.split('_')[1]
            return self.wipe_transition(img1, img2, direction, duration, fps)
        elif transition_type == 'zoom_in':
            return self.zoom_transition(img1, img2, 'in', duration, fps)
        elif transition_type == 'zoom_out':
            return self.zoom_transition(img1, img2, 'out', duration, fps)
        elif transition_type.startswith('slide_'):
            direction = transition_type.split('_')[1]
            return self.slide_transition(img1, img2, direction, duration, fps)
        else:
            # 默认淡入淡出
            return self.fade_transition(img1, img2, duration, fps)
    
    def fade_transition(self, img1, img2, duration, fps):
        """淡入淡出转场"""
        frames = int(duration * fps)
        clips = []
        
        for i in range(frames):
            alpha = i / frames
            # 简单的alpha混合
            blended = Image.blend(img1, img2, alpha)
            clip = ImageClip(np.array(blended)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def push_transition(self, img1, img2, direction, duration, fps):
        """推入转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = Image.new('RGB', (width, height))
            
            if direction == 'left':
                offset = int(progress * width)
                canvas.paste(img1, (-offset, 0))
                canvas.paste(img2, (width - offset, 0))
            elif direction == 'right':
                offset = int(progress * width)
                canvas.paste(img1, (offset, 0))
                canvas.paste(img2, (offset - width, 0))
            elif direction == 'up':
                offset = int(progress * height)
                canvas.paste(img1, (0, -offset))
                canvas.paste(img2, (0, height - offset))
            elif direction == 'down':
                offset = int(progress * height)
                canvas.paste(img1, (0, offset))
                canvas.paste(img2, (0, offset - height))
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def wipe_transition(self, img1, img2, direction, duration, fps):
        """擦除转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = img1.copy()
            
            if direction == 'left':
                wipe_width = int(progress * width)
                if wipe_width > 0:
                    img2_section = img2.crop((0, 0, wipe_width, height))
                    canvas.paste(img2_section, (0, 0))
            elif direction == 'right':
                wipe_width = int(progress * width)
                if wipe_width > 0:
                    start_x = width - wipe_width
                    img2_section = img2.crop((start_x, 0, width, height))
                    canvas.paste(img2_section, (start_x, 0))
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def zoom_transition(self, img1, img2, zoom_type, duration, fps):
        """缩放转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = img1.copy()
            
            if zoom_type == 'in':
                scale = 0.1 + progress * 0.9
            else:  # zoom_out
                scale = 1.0 + progress * 0.5
            
            new_size = (int(width * scale), int(height * scale))
            scaled_img2 = img2.resize(new_size, Image.Resampling.LANCZOS)
            
            # 居中粘贴
            paste_x = (width - new_size[0]) // 2
            paste_y = (height - new_size[1]) // 2
            
            if scale <= 1.0:
                canvas.paste(scaled_img2, (paste_x, paste_y))
            else:
                # 裁剪放大的图片
                crop_x = (new_size[0] - width) // 2
                crop_y = (new_size[1] - height) // 2
                cropped = scaled_img2.crop((crop_x, crop_y, crop_x + width, crop_y + height))
                canvas = cropped
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def slide_transition(self, img1, img2, direction, duration, fps):
        """滑动转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = Image.new('RGB', (width, height))
            
            if direction == 'left':
                img1_x = -int(progress * width)
                img2_x = width - int(progress * width)
                canvas.paste(img1, (img1_x, 0))
                canvas.paste(img2, (img2_x, 0))
            elif direction == 'right':
                img1_x = int(progress * width)
                img2_x = int(progress * width) - width
                canvas.paste(img1, (img1_x, 0))
                canvas.paste(img2, (img2_x, 0))
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)

    def scan_media_files(self, input_dir):
        """扫描媒体文件"""
        media_files = []
        input_path = Path(input_dir)

        for file_path in input_path.rglob('*'):
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in self.supported_image_formats:
                    media_files.append({
                        'path': str(file_path),
                        'type': 'image',
                        'date': self.extract_date_from_image(str(file_path))
                    })
                elif ext in self.supported_video_formats:
                    media_files.append({
                        'path': str(file_path),
                        'type': 'video',
                        'date': datetime.fromtimestamp(file_path.stat().st_mtime)
                    })

        # 按日期排序
        media_files.sort(key=lambda x: x['date'])
        return media_files

    def assign_transitions(self, media_count):
        """为每个媒体文件分配不同的转场效果"""
        assigned = []
        available = self.transitions.copy()

        for i in range(media_count - 1):  # 转场数量比媒体文件少1
            if not available:
                available = self.transitions.copy()

            # 避免连续相同转场
            if i > 0 and assigned[-1] in available:
                available.remove(assigned[-1])

            transition = random.choice(available)
            assigned.append(transition)
            available.remove(transition)

        return assigned

    def process_background_music(self, music_path, total_duration):
        """处理背景音乐"""
        if not music_path or not os.path.exists(music_path):
            return None

        try:
            audio = AudioFileClip(music_path)

            if audio.duration > total_duration:
                # 音乐太长，裁剪
                audio = audio.subclip(0, total_duration)
            else:
                # 音乐太短，循环
                loop_count = int(total_duration / audio.duration) + 1
                audio = concatenate_audioclips([audio] * loop_count)
                audio = audio.subclip(0, total_duration)

            # 添加淡入淡出
            fade_in = self.config['music']['fade_in']
            fade_out = self.config['music']['fade_out']

            if audio.duration > fade_in + fade_out:
                audio = audio.audio_fadein(fade_in).audio_fadeout(fade_out)

            # 调整音量
            audio = audio.volumex(self.config['music']['volume'])

            return audio
        except Exception as e:
            print(f"处理音乐文件失败: {e}")
            return None

    def create_slideshow(self, input_dir, output_path, music_path=None):
        """创建幻灯片视频"""
        print("扫描媒体文件...")
        media_files = self.scan_media_files(input_dir)

        if not media_files:
            print("未找到支持的媒体文件")
            return False

        print(f"找到 {len(media_files)} 个媒体文件")

        # 分配转场效果
        transitions = self.assign_transitions(len(media_files))

        # 处理每个媒体文件
        clips = []
        target_size = self.config['output']['resolution']
        slide_duration = self.config['slideshow']['slide_duration']
        transition_duration = self.config['slideshow']['transition_duration']

        print("处理媒体文件...")
        for i, media in enumerate(media_files):
            print(f"处理 {i+1}/{len(media_files)}: {Path(media['path']).name}")

            if media['type'] == 'image':
                # 处理图片
                img = Image.open(media['path'])
                img = self.resize_image(img, target_size)

                # 添加日期文字
                if self.config['date_text']['show_date']:
                    date_text = self.format_date(media['date'])
                    img = self.add_date_text(img, date_text)

                # 创建图片剪辑
                img_clip = ImageClip(np.array(img)).set_duration(slide_duration)
                clips.append(img_clip)

            elif media['type'] == 'video':
                # 处理视频 - 提取第一帧作为静态图片
                try:
                    video_clip = VideoFileClip(media['path'])
                    # 提取第一帧
                    first_frame = video_clip.get_frame(0)
                    video_clip.close()

                    # 转换为PIL图像
                    img = Image.fromarray(first_frame.astype('uint8'))
                    img = self.resize_image(img, target_size)

                    # 添加日期文字
                    if self.config['date_text']['show_date']:
                        date_text = self.format_date(media['date'])
                        img = self.add_date_text(img, date_text)

                    # 创建图片剪辑
                    img_clip = ImageClip(np.array(img)).set_duration(slide_duration)
                    clips.append(img_clip)
                except Exception as e:
                    print(f"处理视频失败 {media['path']}: {e}")
                    continue

        # 添加转场效果
        print("添加转场效果...")
        final_clips = []

        for i in range(len(clips)):
            final_clips.append(clips[i])

            # 添加转场（除了最后一个）
            if i < len(clips) - 1:
                # 获取当前和下一个图片
                current_img = None
                next_img = None

                if media_files[i]['type'] == 'image':
                    current_img = Image.open(media_files[i]['path'])
                    current_img = self.resize_image(current_img, target_size)
                    if self.config['date_text']['show_date']:
                        date_text = self.format_date(media_files[i]['date'])
                        current_img = self.add_date_text(current_img, date_text)

                if media_files[i+1]['type'] == 'image':
                    next_img = Image.open(media_files[i+1]['path'])
                    next_img = self.resize_image(next_img, target_size)
                    if self.config['date_text']['show_date']:
                        date_text = self.format_date(media_files[i+1]['date'])
                        next_img = self.add_date_text(next_img, date_text)

                # 只有两个都是图片才添加转场
                if current_img and next_img:
                    transition_clip = self.create_transition_clip(
                        current_img, next_img, transitions[i], transition_duration
                    )
                    final_clips.append(transition_clip)

        # 合并所有剪辑
        print("合并视频剪辑...")
        final_video = concatenate_videoclips(final_clips)

        # 添加背景音乐
        if music_path:
            print("添加背景音乐...")
            background_audio = self.process_background_music(music_path, final_video.duration)
            if background_audio:
                final_video = final_video.set_audio(background_audio)

        # 输出视频
        print(f"导出视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            fps=self.config['output']['fps'],
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )

        print("视频创建完成！")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='iPhone风格回忆幻灯片生成器')
    parser.add_argument('--input', '-i', required=True, help='输入图片目录')
    parser.add_argument('--output', '-o', default='memory_slideshow.mp4', help='输出视频文件')
    parser.add_argument('--music', '-m', help='背景音乐文件')
    parser.add_argument('--slide-duration', type=float, default=3.0, help='每张图片显示时长(秒)')
    parser.add_argument('--transition-duration', type=float, default=1.0, help='转场效果时长(秒)')
    parser.add_argument('--no-date', action='store_true', help='不显示日期文字')
    parser.add_argument('--resolution', default='1920x1080', help='输出分辨率 (如: 1920x1080)')

    args = parser.parse_args()

    # 解析分辨率
    try:
        width, height = map(int, args.resolution.split('x'))
        resolution = (width, height)
    except:
        print("分辨率格式错误，使用默认值 1920x1080")
        resolution = (1920, 1080)

    # 创建配置
    config = {
        'output': {
            'resolution': resolution,
            'fps': 30,
            'quality': 'high'
        },
        'slideshow': {
            'slide_duration': args.slide_duration,
            'transition_duration': args.transition_duration
        },
        'date_text': {
            'show_date': not args.no_date,
            'font_size': 36,
            'color': 'white',
            'position': 'bottom_right',
            'margin': (50, 50),
            'shadow': True
        },
        'music': {
            'fade_in': 2.0,
            'fade_out': 3.0,
            'volume': 0.7
        }
    }

    # 创建生成器并运行
    generator = MemorySlideshowGenerator(config)
    success = generator.create_slideshow(args.input, args.output, args.music)

    if success:
        print(f"\n✅ 成功创建回忆视频: {args.output}")
    else:
        print("\n❌ 创建视频失败")
        sys.exit(1)


if __name__ == '__main__':
    main()
