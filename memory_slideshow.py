#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone风格回忆幻灯片生成器
精简但功能完整的实现
"""

import os
import sys
import random
import argparse
from datetime import datetime
from pathlib import Path
import json

try:
    from PIL import Image, ImageDraw, ImageFont, ImageFilter
    import numpy as np
    from moviepy.editor import *
    import exifread
except ImportError as e:
    print(f"缺少依赖库: {e}")
    print("请运行: pip install pillow moviepy exifread numpy")
    sys.exit(1)


class MemorySlideshowGenerator:
    """iPhone风格回忆幻灯片生成器"""
    
    def __init__(self, config=None):
        self.config = config or self.default_config()
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        self.supported_video_formats = {'.mp4', '.mov', '.avi', '.mkv'}
        self.supported_audio_formats = {'.mp3', '.wav', '.aac', '.m4a'}
        
        # 转场效果列表 - 更多PPT风格效果
        self.transitions = [
            'fade', 'push_left', 'push_right', 'push_up', 'push_down',
            'wipe_left', 'wipe_right', 'zoom_in', 'zoom_out', 'slide_left',
            'slide_right', 'rotate_in', 'blinds_h', 'blinds_v', 'checkerboard',
            'split_horizontal', 'split_vertical', 'box_in', 'box_out', 'circle_in',
            'diamond_in', 'random_bars', 'newsflash', 'plus_in', 'wedge'
        ]
    
    def default_config(self):
        """默认配置"""
        return {
            'output': {
                'resolution': (1920, 1080),
                'fps': 30,
                'quality': 'high'
            },
            'slideshow': {
                'slide_duration': 3.0,
                'transition_duration': 1.0,
                'title': '美好回忆',
                'show_title_page': True,
                'title_page_duration': 3.0
            },
            'title_text': {
                'font_size': 72,
                'color': 'white',
                'position': 'center',
                'shadow': True
            },
            'date_text': {
                'show_date': True,
                'font_size': 36,
                'color': 'white',
                'position': 'bottom_center',
                'margin': (0, 100),
                'shadow': True
            },
            'music': {
                'fade_in': 2.0,
                'fade_out': 3.0,
                'volume': 0.7
            }
        }
    
    def extract_date_from_image(self, image_path):
        """从图片EXIF提取拍摄日期"""
        try:
            with open(image_path, 'rb') as f:
                tags = exifread.process_file(f, stop_tag='EXIF DateTimeOriginal')
                if 'EXIF DateTimeOriginal' in tags:
                    date_str = str(tags['EXIF DateTimeOriginal'])
                    return datetime.strptime(date_str, '%Y:%m:%d %H:%M:%S')
        except:
            pass
        return datetime.now()
    
    def format_date(self, date_obj):
        """格式化日期显示"""
        return date_obj.strftime('%Y年%m月%d日')
    
    def add_text_overlay(self, image, text, text_type='date'):
        """在图片上添加文字叠加"""
        img = image.copy()
        draw = ImageDraw.Draw(img)

        # 根据文字类型选择配置
        if text_type == 'title':
            config = self.config['title_text']
        else:
            config = self.config['date_text']

        # 尝试加载字体
        try:
            font = ImageFont.truetype("msyh.ttc", config['font_size'])
        except:
            try:
                font = ImageFont.truetype("arial.ttf", config['font_size'])
            except:
                font = ImageFont.load_default()

        # 计算文字位置
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # 根据位置配置计算坐标
        if config['position'] == 'center':
            x = (img.width - text_width) // 2
            y = (img.height - text_height) // 2
        elif config['position'] == 'bottom_center':
            margin_x, margin_y = config.get('margin', (0, 50))
            x = (img.width - text_width) // 2
            y = img.height - text_height - margin_y
        else:
            # 默认右下角
            margin_x, margin_y = config.get('margin', (50, 50))
            x = img.width - text_width - margin_x
            y = img.height - text_height - margin_y

        # 添加阴影
        if config.get('shadow', False):
            draw.text((x+3, y+3), text, font=font, fill='black')

        # 添加文字
        draw.text((x, y), text, font=font, fill=config['color'])

        return img

    def create_title_page(self, title, date_range):
        """创建标题页"""
        target_size = self.config['output']['resolution']

        # 创建渐变背景
        img = Image.new('RGB', target_size, (20, 30, 50))

        # 添加标题
        img = self.add_text_overlay(img, title, 'title')

        # 添加日期范围
        if date_range:
            date_text = f"{date_range[0].strftime('%Y年%m月')} - {date_range[1].strftime('%Y年%m月')}"
            img = self.add_text_overlay(img, date_text, 'date')

        return img
    
    def resize_image(self, image, target_size, fit_mode='cover'):
        """调整图片尺寸，保持比例，无黑边
        fit_mode: 'cover' - 填满画面裁剪（无黑边），'contain' - 完整显示图片（有黑边）
        """
        img_ratio = image.width / image.height
        target_ratio = target_size[0] / target_size[1]

        if fit_mode == 'contain':
            # 完整显示图片，添加黑边
            if img_ratio > target_ratio:
                # 图片更宽，以宽度为准
                new_width = target_size[0]
                new_height = int(new_width / img_ratio)
            else:
                # 图片更高，以高度为准
                new_height = target_size[1]
                new_width = int(new_height * img_ratio)

            resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 创建黑色背景
            result = Image.new('RGB', target_size, (0, 0, 0))

            # 居中粘贴
            paste_x = (target_size[0] - new_width) // 2
            paste_y = (target_size[1] - new_height) // 2
            result.paste(resized, (paste_x, paste_y))

            return result
        else:
            # 填满画面模式 - 无黑边，智能裁剪
            if img_ratio > target_ratio:
                # 图片更宽，以高度为准，裁剪左右
                new_height = target_size[1]
                new_width = int(new_height * img_ratio)
            else:
                # 图片更高，以宽度为准，裁剪上下
                new_width = target_size[0]
                new_height = int(new_width / img_ratio)

            resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 智能居中裁剪
            left = (new_width - target_size[0]) // 2
            top = (new_height - target_size[1]) // 2
            right = left + target_size[0]
            bottom = top + target_size[1]

            return resized.crop((left, top, right, bottom))
    
    def create_transition_clip(self, img1, img2, transition_type, duration):
        """创建转场效果"""
        fps = self.config['output']['fps']

        if transition_type == 'fade':
            return self.fade_transition(img1, img2, duration, fps)
        elif transition_type.startswith('push_'):
            direction = transition_type.split('_')[1]
            return self.push_transition(img1, img2, direction, duration, fps)
        elif transition_type.startswith('wipe_'):
            direction = transition_type.split('_')[1]
            return self.wipe_transition(img1, img2, direction, duration, fps)
        elif transition_type == 'zoom_in':
            return self.zoom_transition(img1, img2, 'in', duration, fps)
        elif transition_type == 'zoom_out':
            return self.zoom_transition(img1, img2, 'out', duration, fps)
        elif transition_type.startswith('slide_'):
            direction = transition_type.split('_')[1]
            return self.slide_transition(img1, img2, direction, duration, fps)
        elif transition_type == 'split_horizontal':
            return self.split_transition(img1, img2, 'horizontal', duration, fps)
        elif transition_type == 'split_vertical':
            return self.split_transition(img1, img2, 'vertical', duration, fps)
        elif transition_type == 'box_in':
            return self.box_transition(img1, img2, 'in', duration, fps)
        elif transition_type == 'box_out':
            return self.box_transition(img1, img2, 'out', duration, fps)
        elif transition_type == 'circle_in':
            return self.circle_transition(img1, img2, duration, fps)
        elif transition_type == 'diamond_in':
            return self.diamond_transition(img1, img2, duration, fps)
        elif transition_type == 'random_bars':
            return self.random_bars_transition(img1, img2, duration, fps)
        elif transition_type == 'newsflash':
            return self.newsflash_transition(img1, img2, duration, fps)
        else:
            # 默认淡入淡出
            return self.fade_transition(img1, img2, duration, fps)
    
    def fade_transition(self, img1, img2, duration, fps):
        """淡入淡出转场"""
        frames = int(duration * fps)
        clips = []
        
        for i in range(frames):
            alpha = i / frames
            # 简单的alpha混合
            blended = Image.blend(img1, img2, alpha)
            clip = ImageClip(np.array(blended)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def push_transition(self, img1, img2, direction, duration, fps):
        """推入转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = Image.new('RGB', (width, height))
            
            if direction == 'left':
                offset = int(progress * width)
                canvas.paste(img1, (-offset, 0))
                canvas.paste(img2, (width - offset, 0))
            elif direction == 'right':
                offset = int(progress * width)
                canvas.paste(img1, (offset, 0))
                canvas.paste(img2, (offset - width, 0))
            elif direction == 'up':
                offset = int(progress * height)
                canvas.paste(img1, (0, -offset))
                canvas.paste(img2, (0, height - offset))
            elif direction == 'down':
                offset = int(progress * height)
                canvas.paste(img1, (0, offset))
                canvas.paste(img2, (0, offset - height))
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def wipe_transition(self, img1, img2, direction, duration, fps):
        """擦除转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = img1.copy()
            
            if direction == 'left':
                wipe_width = int(progress * width)
                if wipe_width > 0:
                    img2_section = img2.crop((0, 0, wipe_width, height))
                    canvas.paste(img2_section, (0, 0))
            elif direction == 'right':
                wipe_width = int(progress * width)
                if wipe_width > 0:
                    start_x = width - wipe_width
                    img2_section = img2.crop((start_x, 0, width, height))
                    canvas.paste(img2_section, (start_x, 0))
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def zoom_transition(self, img1, img2, zoom_type, duration, fps):
        """缩放转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = img1.copy()
            
            if zoom_type == 'in':
                scale = 0.1 + progress * 0.9
            else:  # zoom_out
                scale = 1.0 + progress * 0.5
            
            new_size = (int(width * scale), int(height * scale))
            scaled_img2 = img2.resize(new_size, Image.Resampling.LANCZOS)
            
            # 居中粘贴
            paste_x = (width - new_size[0]) // 2
            paste_y = (height - new_size[1]) // 2
            
            if scale <= 1.0:
                canvas.paste(scaled_img2, (paste_x, paste_y))
            else:
                # 裁剪放大的图片
                crop_x = (new_size[0] - width) // 2
                crop_y = (new_size[1] - height) // 2
                cropped = scaled_img2.crop((crop_x, crop_y, crop_x + width, crop_y + height))
                canvas = cropped
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)
    
    def slide_transition(self, img1, img2, direction, duration, fps):
        """滑动转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        
        for i in range(frames):
            progress = i / frames
            canvas = Image.new('RGB', (width, height))
            
            if direction == 'left':
                img1_x = -int(progress * width)
                img2_x = width - int(progress * width)
                canvas.paste(img1, (img1_x, 0))
                canvas.paste(img2, (img2_x, 0))
            elif direction == 'right':
                img1_x = int(progress * width)
                img2_x = int(progress * width) - width
                canvas.paste(img1, (img1_x, 0))
                canvas.paste(img2, (img2_x, 0))
            
            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)
        
        return concatenate_videoclips(clips)

    def split_transition(self, img1, img2, direction, duration, fps):
        """分割转场效果 - 上下或左右分割同时显示两张图片"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size

        for i in range(frames):
            progress = i / frames
            canvas = Image.new('RGB', (width, height))

            if direction == 'horizontal':
                # 水平分割 - 上下显示
                split_y = int(height * progress)
                # 上半部分显示img2
                if split_y > 0:
                    img2_top = img2.crop((0, 0, width, split_y))
                    canvas.paste(img2_top, (0, 0))
                # 下半部分显示img1
                if split_y < height:
                    img1_bottom = img1.crop((0, split_y, width, height))
                    canvas.paste(img1_bottom, (0, split_y))
            else:
                # 垂直分割 - 左右显示
                split_x = int(width * progress)
                # 左半部分显示img2
                if split_x > 0:
                    img2_left = img2.crop((0, 0, split_x, height))
                    canvas.paste(img2_left, (0, 0))
                # 右半部分显示img1
                if split_x < width:
                    img1_right = img1.crop((split_x, 0, width, height))
                    canvas.paste(img1_right, (split_x, 0))

            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)

        return concatenate_videoclips(clips)

    def box_transition(self, img1, img2, box_type, duration, fps):
        """盒子转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size

        for i in range(frames):
            progress = i / frames
            canvas = img1.copy()

            if box_type == 'in':
                # 从中心向外扩展
                box_width = int(width * progress)
                box_height = int(height * progress)
            else:
                # 从外向中心收缩
                box_width = int(width * (1 - progress))
                box_height = int(height * (1 - progress))

            # 计算盒子位置
            left = (width - box_width) // 2
            top = (height - box_height) // 2
            right = left + box_width
            bottom = top + box_height

            if box_width > 0 and box_height > 0:
                # 从img2裁剪对应区域
                img2_section = img2.crop((left, top, right, bottom))
                canvas.paste(img2_section, (left, top))

            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)

        return concatenate_videoclips(clips)

    def circle_transition(self, img1, img2, duration, fps):
        """圆形转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        center_x, center_y = width // 2, height // 2
        max_radius = int(((width ** 2 + height ** 2) ** 0.5) / 2)

        for i in range(frames):
            progress = i / frames
            radius = int(max_radius * progress)

            canvas = img1.copy()
            mask = Image.new('L', (width, height), 0)
            mask_draw = ImageDraw.Draw(mask)

            # 绘制圆形遮罩
            mask_draw.ellipse([center_x - radius, center_y - radius,
                              center_x + radius, center_y + radius], fill=255)

            # 应用遮罩
            canvas.paste(img2, (0, 0), mask)

            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)

        return concatenate_videoclips(clips)

    def diamond_transition(self, img1, img2, duration, fps):
        """钻石转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        center_x, center_y = width // 2, height // 2

        for i in range(frames):
            progress = i / frames
            size = int(min(width, height) * progress)

            canvas = img1.copy()
            mask = Image.new('L', (width, height), 0)
            mask_draw = ImageDraw.Draw(mask)

            # 绘制钻石形状
            points = [
                (center_x, center_y - size // 2),  # 上
                (center_x + size // 2, center_y),  # 右
                (center_x, center_y + size // 2),  # 下
                (center_x - size // 2, center_y)   # 左
            ]
            mask_draw.polygon(points, fill=255)

            # 应用遮罩
            canvas.paste(img2, (0, 0), mask)

            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)

        return concatenate_videoclips(clips)

    def random_bars_transition(self, img1, img2, duration, fps):
        """随机条纹转场效果"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size
        bar_count = 20
        bar_height = height // bar_count

        # 生成随机顺序
        bar_order = list(range(bar_count))
        random.shuffle(bar_order)

        for i in range(frames):
            progress = i / frames
            revealed_bars = int(bar_count * progress)

            canvas = img1.copy()

            for j in range(revealed_bars):
                if j < len(bar_order):
                    bar_index = bar_order[j]
                    y_start = bar_index * bar_height
                    y_end = min((bar_index + 1) * bar_height, height)

                    img2_section = img2.crop((0, y_start, width, y_end))
                    canvas.paste(img2_section, (0, y_start))

            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)

        return concatenate_videoclips(clips)

    def newsflash_transition(self, img1, img2, duration, fps):
        """新闻快报转场效果 - 四个角同时向中心"""
        frames = int(duration * fps)
        clips = []
        width, height = img1.size

        for i in range(frames):
            progress = i / frames
            reveal_size = int(min(width, height) * progress / 2)

            canvas = img1.copy()

            # 四个角的矩形区域
            corners = [
                (0, 0, reveal_size, reveal_size),  # 左上
                (width - reveal_size, 0, width, reveal_size),  # 右上
                (0, height - reveal_size, reveal_size, height),  # 左下
                (width - reveal_size, height - reveal_size, width, height)  # 右下
            ]

            for corner in corners:
                if corner[2] > corner[0] and corner[3] > corner[1]:
                    img2_section = img2.crop(corner)
                    canvas.paste(img2_section, (corner[0], corner[1]))

            clip = ImageClip(np.array(canvas)).set_duration(1/fps)
            clips.append(clip)

        return concatenate_videoclips(clips)

    def scan_media_files(self, input_dir):
        """扫描媒体文件"""
        media_files = []
        input_path = Path(input_dir)

        for file_path in input_path.rglob('*'):
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext in self.supported_image_formats:
                    media_files.append({
                        'path': str(file_path),
                        'type': 'image',
                        'date': self.extract_date_from_image(str(file_path))
                    })
                elif ext in self.supported_video_formats:
                    media_files.append({
                        'path': str(file_path),
                        'type': 'video',
                        'date': datetime.fromtimestamp(file_path.stat().st_mtime)
                    })

        # 按日期排序
        media_files.sort(key=lambda x: x['date'])
        return media_files

    def assign_transitions(self, media_count):
        """为每个媒体文件分配不同的转场效果"""
        assigned = []
        available = self.transitions.copy()

        for i in range(media_count - 1):  # 转场数量比媒体文件少1
            if not available:
                available = self.transitions.copy()

            # 避免连续相同转场
            if i > 0 and assigned[-1] in available:
                available.remove(assigned[-1])

            transition = random.choice(available)
            assigned.append(transition)
            available.remove(transition)

        return assigned

    def process_background_music(self, music_path, total_duration):
        """处理背景音乐"""
        if not music_path or not os.path.exists(music_path):
            print(f"音乐文件不存在: {music_path}")
            return None

        try:
            print(f"加载音频文件: {music_path}")
            audio = AudioFileClip(music_path)
            print(f"原始音频时长: {audio.duration:.2f}秒")

            if audio.duration > total_duration:
                # 音乐太长，裁剪
                print(f"音乐太长，裁剪到 {total_duration:.2f}秒")
                audio = audio.subclip(0, total_duration)
            else:
                # 音乐太短，循环
                loop_count = int(total_duration / audio.duration) + 1
                print(f"音乐太短，循环 {loop_count} 次")
                audio = concatenate_audioclips([audio] * loop_count)
                audio = audio.subclip(0, total_duration)

            # 添加淡入淡出
            fade_in = self.config['music']['fade_in']
            fade_out = self.config['music']['fade_out']

            if audio.duration > fade_in + fade_out:
                print(f"添加淡入淡出效果: {fade_in}s / {fade_out}s")
                audio = audio.audio_fadein(fade_in).audio_fadeout(fade_out)

            # 调整音量
            volume = self.config['music']['volume']
            print(f"调整音量到: {volume}")
            audio = audio.volumex(volume)

            print(f"最终音频时长: {audio.duration:.2f}秒")
            return audio
        except Exception as e:
            print(f"处理音乐文件失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_slideshow(self, input_dir, output_path, music_path=None):
        """创建幻灯片视频"""
        print("扫描媒体文件...")
        media_files = self.scan_media_files(input_dir)

        if not media_files:
            print("未找到支持的媒体文件")
            return False

        print(f"找到 {len(media_files)} 个媒体文件")

        # 分配转场效果
        transitions = self.assign_transitions(len(media_files))

        # 处理每个媒体文件
        clips = []
        target_size = self.config['output']['resolution']
        slide_duration = self.config['slideshow']['slide_duration']
        transition_duration = self.config['slideshow']['transition_duration']

        # 创建标题页
        if self.config['slideshow']['show_title_page']:
            print("创建标题页...")
            date_range = (media_files[0]['date'], media_files[-1]['date']) if media_files else None
            title_img = self.create_title_page(self.config['slideshow']['title'], date_range)
            title_clip = ImageClip(np.array(title_img)).set_duration(self.config['slideshow']['title_page_duration'])
            clips.append(title_clip)

        print("处理媒体文件...")
        for i, media in enumerate(media_files):
            print(f"处理 {i+1}/{len(media_files)}: {Path(media['path']).name}")

            if media['type'] == 'image':
                # 处理图片 - 根据配置选择显示模式
                img = Image.open(media['path'])
                fit_mode = self.config.get('image_processing', {}).get('fit_mode', 'cover')
                img = self.resize_image(img, target_size, fit_mode)

                # 添加日期文字
                if self.config['date_text']['show_date']:
                    date_text = self.format_date(media['date'])
                    img = self.add_text_overlay(img, date_text, 'date')

                # 创建图片剪辑
                img_clip = ImageClip(np.array(img)).set_duration(slide_duration)
                clips.append(img_clip)

            elif media['type'] == 'video':
                # 处理视频 - 提取第一帧作为静态图片
                try:
                    video_clip = VideoFileClip(media['path'])
                    # 提取第一帧
                    first_frame = video_clip.get_frame(0)
                    video_clip.close()

                    # 转换为PIL图像
                    img = Image.fromarray(first_frame.astype('uint8'))
                    fit_mode = self.config.get('image_processing', {}).get('fit_mode', 'cover')
                    img = self.resize_image(img, target_size, fit_mode)

                    # 添加日期文字
                    if self.config['date_text']['show_date']:
                        date_text = self.format_date(media['date'])
                        img = self.add_text_overlay(img, date_text, 'date')

                    # 创建图片剪辑
                    img_clip = ImageClip(np.array(img)).set_duration(slide_duration)
                    clips.append(img_clip)
                except Exception as e:
                    print(f"处理视频失败 {media['path']}: {e}")
                    continue

        # 添加转场效果
        print("添加转场效果...")
        final_clips = []

        # 计算标题页偏移
        title_offset = 1 if self.config['slideshow']['show_title_page'] else 0

        for i in range(len(clips)):
            final_clips.append(clips[i])

            # 跳过标题页的转场处理
            if i == 0 and title_offset > 0:
                continue

            # 添加转场（除了最后一个）
            if i < len(clips) - 1:
                # 计算在media_files中的实际索引
                media_index = i - title_offset
                next_media_index = media_index + 1

                # 确保索引有效
                if media_index < 0 or next_media_index >= len(media_files):
                    continue

                # 获取当前和下一个图片
                current_img = None
                next_img = None

                if media_files[media_index]['type'] == 'image':
                    current_img = Image.open(media_files[media_index]['path'])
                    current_img = self.resize_image(current_img, target_size, 'cover')
                    if self.config['date_text']['show_date']:
                        date_text = self.format_date(media_files[media_index]['date'])
                        current_img = self.add_text_overlay(current_img, date_text, 'date')

                if media_files[next_media_index]['type'] == 'image':
                    next_img = Image.open(media_files[next_media_index]['path'])
                    next_img = self.resize_image(next_img, target_size, 'cover')
                    if self.config['date_text']['show_date']:
                        date_text = self.format_date(media_files[next_media_index]['date'])
                        next_img = self.add_text_overlay(next_img, date_text, 'date')

                # 只有两个都是图片才添加转场
                if current_img and next_img:
                    transition_index = media_index if media_index < len(transitions) else 0
                    transition_clip = self.create_transition_clip(
                        current_img, next_img, transitions[transition_index], transition_duration
                    )
                    final_clips.append(transition_clip)

        # 合并所有剪辑
        print("合并视频剪辑...")
        final_video = concatenate_videoclips(final_clips)

        # 添加背景音乐
        if music_path:
            print(f"添加背景音乐: {music_path}")
            print(f"视频时长: {final_video.duration:.2f}秒")
            background_audio = self.process_background_music(music_path, final_video.duration)
            if background_audio:
                print(f"音频处理成功，时长: {background_audio.duration:.2f}秒")
                final_video = final_video.set_audio(background_audio)
            else:
                print("音频处理失败")

        # 输出视频
        print(f"导出视频到: {output_path}")

        # 检查音频状态
        if final_video.audio:
            print(f"✅ 视频包含音频，时长: {final_video.audio.duration:.2f}秒")
        else:
            print("⚠️ 视频不包含音频")

        # 输出视频，尝试多种音频编码方式
        success = False

        if final_video.audio:
            # 优先使用兼容性最好的MP3编码
            audio_configs = [
                {
                    'name': 'MP3编码（推荐）',
                    'params': {
                        'fps': self.config['output']['fps'],
                        'codec': 'libx264',
                        'audio_codec': 'mp3',
                        'audio_bitrate': '192k',
                        'verbose': False,
                        'logger': None
                    }
                },
                {
                    'name': '默认编码',
                    'params': {
                        'fps': self.config['output']['fps'],
                        'codec': 'libx264',
                        'verbose': False,
                        'logger': None
                    }
                },
                {
                    'name': '无压缩音频',
                    'params': {
                        'fps': self.config['output']['fps'],
                        'codec': 'libx264',
                        'audio_codec': 'pcm_s16le',
                        'verbose': False,
                        'logger': None
                    }
                },
                {
                    'name': 'AAC编码（备用）',
                    'params': {
                        'fps': self.config['output']['fps'],
                        'codec': 'libx264',
                        'audio_codec': 'aac',
                        'audio_bitrate': '128k',
                        'verbose': False,
                        'logger': None
                    }
                }
            ]

            for config in audio_configs:
                try:
                    print(f"尝试{config['name']}...")
                    final_video.write_videofile(output_path, **config['params'])
                    print(f"✅ {config['name']}成功")
                    success = True
                    break
                except Exception as e:
                    print(f"❌ {config['name']}失败: {e}")
                    continue
        else:
            # 无音频的情况
            try:
                print("导出无音频视频...")
                final_video.write_videofile(
                    output_path,
                    fps=self.config['output']['fps'],
                    codec='libx264',
                    verbose=False,
                    logger=None
                )
                success = True
            except Exception as e:
                print(f"无音频视频导出失败: {e}")

        if not success:
            print("❌ 所有导出方法都失败了")
            return False

        print("视频创建完成！")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='iPhone风格回忆幻灯片生成器')
    parser.add_argument('--input', '-i', required=True, help='输入图片目录')
    parser.add_argument('--output', '-o', default='memory_slideshow.mp4', help='输出视频文件')
    parser.add_argument('--music', '-m', help='背景音乐文件')
    parser.add_argument('--title', '-t', default='美好回忆', help='视频主题标题')
    parser.add_argument('--slide-duration', type=float, default=3.0, help='每张图片显示时长(秒)')
    parser.add_argument('--transition-duration', type=float, default=1.0, help='转场效果时长(秒)')
    parser.add_argument('--no-date', action='store_true', help='不显示日期文字')
    parser.add_argument('--no-title-page', action='store_true', help='不显示标题页')
    parser.add_argument('--keep-black-bars', action='store_true', help='保留黑边，完整显示图片')
    parser.add_argument('--resolution', default='1920x1080', help='输出分辨率 (如: 1920x1080)')

    args = parser.parse_args()

    # 解析分辨率
    try:
        width, height = map(int, args.resolution.split('x'))
        resolution = (width, height)
    except:
        print("分辨率格式错误，使用默认值 1920x1080")
        resolution = (1920, 1080)

    # 创建配置
    config = {
        'output': {
            'resolution': resolution,
            'fps': 30,
            'quality': 'high'
        },
        'slideshow': {
            'slide_duration': args.slide_duration,
            'transition_duration': args.transition_duration,
            'title': args.title,
            'show_title_page': not args.no_title_page,
            'title_page_duration': 3.0
        },
        'image_processing': {
            'fit_mode': 'contain' if args.keep_black_bars else 'cover'
        },
        'title_text': {
            'font_size': 72,
            'color': 'white',
            'position': 'center',
            'shadow': True
        },
        'date_text': {
            'show_date': not args.no_date,
            'font_size': 36,
            'color': 'white',
            'position': 'bottom_center',
            'margin': (0, 100),
            'shadow': True
        },
        'music': {
            'fade_in': 2.0,
            'fade_out': 3.0,
            'volume': 0.7
        }
    }

    # 创建生成器并运行
    generator = MemorySlideshowGenerator(config)
    success = generator.create_slideshow(args.input, args.output, args.music)

    if success:
        print(f"\n✅ 成功创建回忆视频: {args.output}")
    else:
        print("\n❌ 创建视频失败")
        sys.exit(1)


if __name__ == '__main__':
    main()
