@echo off
chcp 65001 >nul
echo ========================================
echo    iPhone风格回忆幻灯片生成器
echo    (统一分辨率处理 + MP3音频)
echo ========================================
echo.

REM 激活虚拟环境
call photo2video_env\Scripts\activate.bat

REM 检查参数
if "%1"=="" (
    echo 使用方法:
    echo   create_memory.bat 图片目录 [输出文件名] [背景音乐] [主题标题]
    echo.
    echo 示例:
    echo   create_memory.bat ./images
    echo   create_memory.bat ./images "我的回忆.mp4"
    echo   create_memory.bat ./images "我的回忆.mp4" "月亮照山川.mp3"
    echo   create_memory.bat ./images "我的回忆.mp4" "月亮照山川.mp3" "美好时光"
    echo.
    echo 新功能:
    echo   - 所有图片预处理为统一分辨率
    echo   - 无黑边显示（填满画面）
    echo   - MP3音频编码确保兼容性
    echo   - 20种PPT风格转场效果
    echo.
    pause
    exit /b 1
)

REM 设置默认值
set INPUT_DIR=%1
set OUTPUT_FILE=%2
set MUSIC_FILE=%3
set TITLE=%4

if "%OUTPUT_FILE%"=="" set OUTPUT_FILE=memory_slideshow.mp4
if "%TITLE%"=="" set TITLE=美好回忆

echo 输入目录: %INPUT_DIR%
echo 输出文件: %OUTPUT_FILE%
echo 背景音乐: %MUSIC_FILE%
echo 主题标题: %TITLE%
echo.
echo 开始处理...

REM 执行生成命令
if "%MUSIC_FILE%"=="" (
    python memory_slideshow.py --input "%INPUT_DIR%" --output "%OUTPUT_FILE%" --title "%TITLE%"
) else (
    python memory_slideshow.py --input "%INPUT_DIR%" --output "%OUTPUT_FILE%" --music "%MUSIC_FILE%" --title "%TITLE%"
)

echo.
echo ========================================
echo 处理完成！输出文件: %OUTPUT_FILE%
echo.
echo 特性说明:
echo ✅ 统一分辨率处理 - 所有图片标准化
echo ✅ 无黑边显示 - 填满整个画面
echo ✅ MP3音频编码 - 最佳兼容性
echo ✅ 智能转场效果 - 每张图片不同
echo ✅ 标题页展示 - 专业开场
echo ========================================
pause
