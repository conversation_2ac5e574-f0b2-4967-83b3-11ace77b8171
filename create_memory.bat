@echo off
chcp 65001 >nul
echo ========================================
echo    iPhone风格回忆幻灯片生成器
echo ========================================
echo.

REM 激活虚拟环境
call photo2video_env\Scripts\activate.bat

REM 检查参数
if "%1"=="" (
    echo 使用方法:
    echo   create_memory.bat 图片目录 [输出文件名] [背景音乐]
    echo.
    echo 示例:
    echo   create_memory.bat ./images
    echo   create_memory.bat ./images my_memory.mp4
    echo   create_memory.bat ./images my_memory.mp4 background.mp3
    echo.
    pause
    exit /b 1
)

REM 设置默认值
set INPUT_DIR=%1
set OUTPUT_FILE=%2
set MUSIC_FILE=%3

if "%OUTPUT_FILE%"=="" set OUTPUT_FILE=memory_slideshow.mp4
if "%MUSIC_FILE%"=="" (
    python memory_slideshow.py --input "%INPUT_DIR%" --output "%OUTPUT_FILE%"
) else (
    python memory_slideshow.py --input "%INPUT_DIR%" --output "%OUTPUT_FILE%" --music "%MUSIC_FILE%"
)

echo.
echo ========================================
echo 处理完成！输出文件: %OUTPUT_FILE%
echo ========================================
pause
