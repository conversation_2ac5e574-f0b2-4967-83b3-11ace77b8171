#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件示例 - 展示如何自定义配置
"""

from memory_slideshow import MemorySlideshowGenerator

# 示例1: 高质量配置
high_quality_config = {
    'output': {
        'resolution': (3840, 2160),  # 4K分辨率
        'fps': 60,                   # 高帧率
        'quality': 'high'
    },
    'slideshow': {
        'slide_duration': 4.0,       # 较长的显示时间
        'transition_duration': 1.5   # 较长的转场时间
    },
    'date_text': {
        'show_date': True,
        'font_size': 72,             # 大字体
        'color': 'white',
        'position': 'bottom_right',
        'margin': (100, 100),        # 大边距
        'shadow': True
    },
    'music': {
        'fade_in': 3.0,              # 长淡入
        'fade_out': 5.0,             # 长淡出
        'volume': 0.8                # 较高音量
    }
}

# 示例2: 快节奏配置
fast_paced_config = {
    'output': {
        'resolution': (1920, 1080),
        'fps': 30,
        'quality': 'high'
    },
    'slideshow': {
        'slide_duration': 1.5,       # 快速切换
        'transition_duration': 0.3   # 短转场
    },
    'date_text': {
        'show_date': True,
        'font_size': 28,             # 小字体
        'color': 'yellow',           # 醒目颜色
        'position': 'bottom_right',
        'margin': (30, 30),
        'shadow': True
    },
    'music': {
        'fade_in': 1.0,
        'fade_out': 2.0,
        'volume': 0.9
    }
}

# 示例3: 简约配置
minimal_config = {
    'output': {
        'resolution': (1280, 720),   # 较低分辨率
        'fps': 24,                   # 电影帧率
        'quality': 'medium'
    },
    'slideshow': {
        'slide_duration': 3.0,
        'transition_duration': 1.0
    },
    'date_text': {
        'show_date': False,          # 不显示日期
        'font_size': 32,
        'color': 'white',
        'position': 'bottom_right',
        'margin': (50, 50),
        'shadow': False              # 无阴影
    },
    'music': {
        'fade_in': 2.0,
        'fade_out': 3.0,
        'volume': 0.6                # 较低音量
    }
}

# 示例4: 手机竖屏配置
mobile_config = {
    'output': {
        'resolution': (1080, 1920),  # 竖屏分辨率
        'fps': 30,
        'quality': 'high'
    },
    'slideshow': {
        'slide_duration': 2.5,
        'transition_duration': 0.8
    },
    'date_text': {
        'show_date': True,
        'font_size': 48,
        'color': 'white',
        'position': 'top_center',    # 顶部居中
        'margin': (0, 50),
        'shadow': True
    },
    'music': {
        'fade_in': 2.0,
        'fade_out': 3.0,
        'volume': 0.7
    }
}

def create_with_config(config_name, input_dir, output_file, music_file=None):
    """使用指定配置创建视频"""
    configs = {
        'high_quality': high_quality_config,
        'fast_paced': fast_paced_config,
        'minimal': minimal_config,
        'mobile': mobile_config
    }
    
    if config_name not in configs:
        print(f"未知配置: {config_name}")
        return False
    
    config = configs[config_name]
    generator = MemorySlideshowGenerator(config)
    
    print(f"使用 {config_name} 配置创建视频...")
    return generator.create_slideshow(input_dir, output_file, music_file)

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) < 4:
        print("使用方法:")
        print("python config_example.py <配置名> <输入目录> <输出文件> [音乐文件]")
        print()
        print("可用配置:")
        print("  high_quality - 高质量4K配置")
        print("  fast_paced   - 快节奏配置")
        print("  minimal      - 简约配置")
        print("  mobile       - 手机竖屏配置")
        print()
        print("示例:")
        print("python config_example.py high_quality ./images 4k_video.mp4 music.mp3")
        sys.exit(1)
    
    config_name = sys.argv[1]
    input_dir = sys.argv[2]
    output_file = sys.argv[3]
    music_file = sys.argv[4] if len(sys.argv) > 4 else None
    
    success = create_with_config(config_name, input_dir, output_file, music_file)
    
    if success:
        print(f"✅ 成功创建视频: {output_file}")
    else:
        print("❌ 创建视频失败")
        sys.exit(1)
