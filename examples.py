#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iPhone风格回忆幻灯片生成器 - 使用示例
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(cmd):
    """运行命令并显示输出"""
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"执行失败: {e}")
        return False

def example_basic():
    """示例1: 基本使用"""
    print("=" * 60)
    print("示例1: 基本使用 - 创建简单的回忆视频")
    print("=" * 60)
    
    cmd = [
        "python", "memory_slideshow.py",
        "--input", "./images",
        "--output", "basic_memory.mp4"
    ]
    
    return run_command(cmd)

def example_with_music():
    """示例2: 添加背景音乐"""
    print("=" * 60)
    print("示例2: 添加背景音乐")
    print("=" * 60)
    
    cmd = [
        "python", "memory_slideshow.py",
        "--input", "./images",
        "--output", "music_memory.mp4",
        "--music", "月亮照山川.mp3"
    ]
    
    return run_command(cmd)

def example_custom_timing():
    """示例3: 自定义时间设置"""
    print("=" * 60)
    print("示例3: 自定义时间设置 - 快节奏视频")
    print("=" * 60)
    
    cmd = [
        "python", "memory_slideshow.py",
        "--input", "./images",
        "--output", "fast_memory.mp4",
        "--music", "月亮照山川.mp3",
        "--slide-duration", "1.5",
        "--transition-duration", "0.5"
    ]
    
    return run_command(cmd)

def example_high_quality():
    """示例4: 高质量输出"""
    print("=" * 60)
    print("示例4: 高质量输出 - 4K分辨率")
    print("=" * 60)
    
    cmd = [
        "python", "memory_slideshow.py",
        "--input", "./images",
        "--output", "4k_memory.mp4",
        "--music", "月亮照山川.mp3",
        "--resolution", "3840x2160",
        "--slide-duration", "4.0"
    ]
    
    return run_command(cmd)

def example_no_date():
    """示例5: 不显示日期"""
    print("=" * 60)
    print("示例5: 不显示日期文字")
    print("=" * 60)
    
    cmd = [
        "python", "memory_slideshow.py",
        "--input", "./images",
        "--output", "no_date_memory.mp4",
        "--music", "月亮照山川.mp3",
        "--no-date"
    ]
    
    return run_command(cmd)

def example_mobile_format():
    """示例6: 手机格式"""
    print("=" * 60)
    print("示例6: 手机竖屏格式")
    print("=" * 60)
    
    cmd = [
        "python", "memory_slideshow.py",
        "--input", "./images",
        "--output", "mobile_memory.mp4",
        "--music", "月亮照山川.mp3",
        "--resolution", "1080x1920",
        "--slide-duration", "2.5"
    ]
    
    return run_command(cmd)

def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("iPhone风格回忆幻灯片生成器 - 示例演示")
    print("=" * 60)
    print("1. 基本使用 - 创建简单的回忆视频")
    print("2. 添加背景音乐")
    print("3. 自定义时间设置 - 快节奏视频")
    print("4. 高质量输出 - 4K分辨率")
    print("5. 不显示日期文字")
    print("6. 手机竖屏格式")
    print("7. 运行所有示例")
    print("0. 退出")
    print("=" * 60)

def main():
    """主函数"""
    # 检查是否存在必要文件
    if not Path("memory_slideshow.py").exists():
        print("错误: 找不到 memory_slideshow.py 文件")
        return
    
    if not Path("images").exists():
        print("错误: 找不到 images 目录")
        return
    
    examples = {
        '1': example_basic,
        '2': example_with_music,
        '3': example_custom_timing,
        '4': example_high_quality,
        '5': example_no_date,
        '6': example_mobile_format
    }
    
    while True:
        show_menu()
        choice = input("\n请选择示例 (0-7): ").strip()
        
        if choice == '0':
            print("再见！")
            break
        elif choice == '7':
            print("运行所有示例...")
            for i in range(1, 7):
                if str(i) in examples:
                    success = examples[str(i)]()
                    if not success:
                        print(f"示例 {i} 执行失败")
                    print("\n" + "="*60 + "\n")
        elif choice in examples:
            success = examples[choice]()
            if success:
                print(f"\n✅ 示例 {choice} 执行成功！")
            else:
                print(f"\n❌ 示例 {choice} 执行失败！")
        else:
            print("无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == '__main__':
    main()
