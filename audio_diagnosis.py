#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频问题诊断和解决工具
"""

import os
import subprocess
from moviepy.editor import VideoFileClip, AudioFileClip

def check_system_audio():
    """检查系统音频设置"""
    print("🔍 检查系统音频设置...")
    
    try:
        # 检查Windows音频服务
        result = subprocess.run(['sc', 'query', 'AudioSrv'], 
                              capture_output=True, text=True)
        if 'RUNNING' in result.stdout:
            print("✅ Windows音频服务正在运行")
        else:
            print("❌ Windows音频服务未运行")
    except:
        print("⚠️ 无法检查Windows音频服务")
    
    # 检查音频设备
    try:
        result = subprocess.run(['powershell', 'Get-AudioDevice'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 音频设备检测正常")
        else:
            print("⚠️ 音频设备检测异常")
    except:
        print("⚠️ 无法检查音频设备")

def test_original_audio():
    """测试原始音频文件"""
    print("\n🎵 测试原始音频文件...")
    
    audio_file = "月亮照山川.mp3"
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return False
    
    try:
        audio = AudioFileClip(audio_file)
        print(f"✅ 音频文件加载成功")
        print(f"   - 时长: {audio.duration:.2f}秒")
        print(f"   - 采样率: {audio.fps}Hz")
        audio.close()
        return True
    except Exception as e:
        print(f"❌ 音频文件加载失败: {e}")
        return False

def create_simple_test():
    """创建最简单的音频测试"""
    print("\n🧪 创建简单音频测试...")
    
    try:
        # 创建5秒的音频测试
        audio = AudioFileClip("月亮照山川.mp3").subclip(0, 5)
        
        # 直接保存为音频文件
        audio.write_audiofile("test_audio_only.wav", verbose=False)
        print("✅ 纯音频文件创建成功: test_audio_only.wav")
        
        audio.close()
        return True
    except Exception as e:
        print(f"❌ 纯音频文件创建失败: {e}")
        return False

def test_video_players():
    """测试不同播放器的兼容性"""
    print("\n🎬 播放器兼容性建议...")
    
    print("推荐的视频播放器（按兼容性排序）:")
    print("1. VLC Media Player - 最佳兼容性")
    print("2. PotPlayer - 良好兼容性")
    print("3. Windows Media Player - 基本兼容性")
    print("4. 浏览器播放 - 可能有限制")
    
    print("\n如果使用Windows Media Player没有声音，请尝试:")
    print("- 安装K-Lite Codec Pack")
    print("- 更新音频驱动程序")
    print("- 检查播放器音频设置")

def create_compatibility_videos():
    """创建多种兼容性测试视频"""
    print("\n🔧 创建兼容性测试视频...")
    
    try:
        from PIL import Image
        import numpy as np
        from moviepy.editor import ImageClip, concatenate_videoclips
        
        # 创建测试图片
        img = Image.new('RGB', (1920, 1080), (0, 100, 200))
        img_array = np.array(img)
        video_base = ImageClip(img_array).set_duration(3.0)
        
        # 加载音频
        audio = AudioFileClip("月亮照山川.mp3").subclip(0, 3.0)
        
        # 不同的编码配置
        configs = [
            {
                'name': 'Windows兼容',
                'file': 'windows_compatible.mp4',
                'params': {
                    'fps': 25,
                    'codec': 'libx264',
                    'audio_codec': 'aac',
                    'audio_bitrate': '128k'
                }
            },
            {
                'name': '通用兼容',
                'file': 'universal_compatible.mp4', 
                'params': {
                    'fps': 30,
                    'codec': 'libx264',
                    'audio_codec': 'mp3',
                    'audio_bitrate': '192k'
                }
            },
            {
                'name': '高质量',
                'file': 'high_quality.mp4',
                'params': {
                    'fps': 30,
                    'codec': 'libx264',
                    'audio_codec': 'aac',
                    'audio_bitrate': '320k',
                    'bitrate': '5000k'
                }
            }
        ]
        
        for config in configs:
            try:
                print(f"创建 {config['name']} 版本...")
                final_video = video_base.set_audio(audio)
                final_video.write_videofile(
                    config['file'],
                    verbose=False,
                    logger=None,
                    **config['params']
                )
                print(f"✅ {config['name']} 创建成功: {config['file']}")
                final_video.close()
            except Exception as e:
                print(f"❌ {config['name']} 创建失败: {e}")
        
        audio.close()
        video_base.close()
        
    except Exception as e:
        print(f"❌ 兼容性测试视频创建失败: {e}")

def main():
    """主诊断函数"""
    print("🩺 音频问题诊断工具")
    print("=" * 60)
    
    # 1. 检查系统音频
    check_system_audio()
    
    # 2. 测试原始音频
    if not test_original_audio():
        print("❌ 原始音频文件有问题，请检查音频文件")
        return
    
    # 3. 创建简单测试
    create_simple_test()
    
    # 4. 播放器建议
    test_video_players()
    
    # 5. 创建兼容性测试视频
    create_compatibility_videos()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成！解决方案:")
    print("\n1. 首先测试纯音频文件: test_audio_only.wav")
    print("   如果这个文件没有声音，说明是系统音频问题")
    
    print("\n2. 测试兼容性视频:")
    print("   - windows_compatible.mp4 (Windows优化)")
    print("   - universal_compatible.mp4 (通用兼容)")
    print("   - high_quality.mp4 (高质量)")
    
    print("\n3. 如果所有文件都没有声音:")
    print("   - 检查系统音量设置")
    print("   - 重启音频服务")
    print("   - 更新音频驱动")
    print("   - 尝试不同的播放器")
    
    print("\n4. 推荐使用VLC播放器进行测试")

if __name__ == '__main__':
    main()
